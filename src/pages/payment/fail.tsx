import { useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { publicPreLoadSourceObj } from '@/configs/source'

function PaymentFailPage() {
  const { t } = useTranslation()
  const params = useLocation()
  const searchParams = new URLSearchParams(params.search)
  const session_id = searchParams.get('session_id')

  return (
    <div className="flex justify-center items-center h-full w-full flex-col">
      <img src={publicPreLoadSourceObj.payFail} alt="" />
      <div className="text-[3.2rem] font-bold text-white my-20">
        {t('支付失败')}
      </div>
    </div>
  )
}

export default PaymentFailPage
