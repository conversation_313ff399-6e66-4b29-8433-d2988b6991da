import { useEffect, useMemo, useRef, useState } from 'react'
import MazeSingleTemplate from './MazeSingleTemplate'
import { Swiper as SwiperType } from 'swiper/types'
import { TemplateListProps } from './const'
import classNames from 'classnames'

import { Swiper, SwiperSlide } from 'swiper/react'
import { Grid, Navigation } from 'swiper/modules'
// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/grid'
import 'swiper/css/pagination'
import 'swiper/css/autoplay'
import { ThemeDetail } from '@/apis/types'
import { publicPreLoadSourceObj } from '@/configs/source'

import { SvgIcon } from '@/components/ui/SvgIcon'
import { isPhone } from '@/utils'
import { useAtomValue } from 'jotai'
import { screenOrientationAtom } from '@/stores'

// Custom navigation button components
const SwiperNextButton = ({
  swiperRef,
}: {
  swiperRef: React.RefObject<SwiperType | null>
}) => {
  return (
    <div
      className="absolute right-6 top-1/2 -translate-y-1/2 z-10 cursor-pointer hover:scale-110 transition-transform duration-300"
      onClick={() => swiperRef.current?.slideNext()}
    >
      <SvgIcon
        src="/images/icons/arrow-right.svg"
        alt="下一页"
        className="w-24 h-24"
      />
    </div>
  )
}

const SwiperPrevButton = ({
  swiperRef,
}: {
  swiperRef: React.RefObject<SwiperType | null>
}) => {
  return (
    <div
      className="absolute left-6 top-1/2 -translate-y-1/2 z-10 cursor-pointer hover:scale-110 transition-transform duration-300"
      onClick={() => swiperRef.current?.slidePrev()}
    >
      <SvgIcon
        src="/images/icons/arrow-left.svg"
        alt="上一页"
        className="w-24 h-24"
      />
    </div>
  )
}

/** 单行模版列表 */
const MazeSingleTemplateList = ({
  selectTemplateList,
  activeTemplate,
  setActiveTemplate,
  multiline,
  swiperProps = {}, // Default to empty object if not provided
  activeGender,
  listKey,
}: TemplateListProps) => {
  const swiperRef = useRef<SwiperType | null>(null)
  const [isBeginning, setIsBeginning] = useState(true)
  const [isEnd, setIsEnd] = useState(false)
  const [activeSlideRect, setActiveSlideRect] = useState<DOMRect | null>(null)
  const screenOrientation = useAtomValue(screenOrientationAtom)

  const list = useMemo(() => {
    if (multiline && selectTemplateList.length % 4 !== 0) {
      const len = 4 - (selectTemplateList.length % 4)
      return selectTemplateList.concat(selectTemplateList.slice(0, len))
    } else {
      return selectTemplateList
    }
  }, [selectTemplateList])

  // 初始化时获取active slide中图片的位置和大小，之后保持不变
  const initializeFramePosition = () => {
    const swiper = swiperRef.current
    if (swiper && !activeSlideRect) {
      // 只在还没有设置位置时才执行
      const activeSlide = swiper.slides[swiper.activeIndex]
      if (activeSlide) {
        // 查找active slide中的图片元素
        if (activeSlide) {
          const rect = activeSlide.getBoundingClientRect()
          setActiveSlideRect(rect)
        }
      }
    }
  }

  useEffect(() => {
    const swiper = swiperRef.current
    if (swiper) {
      // Update navigation state on slide change
      swiper.on('slideChange', () => {
        setIsBeginning(swiper.isBeginning)
        setIsEnd(swiper.isEnd)

        // Trigger setActiveTemplate when slide changes
        const realIndex = swiper.realIndex
        if (list[realIndex]) {
          setActiveTemplate(list[realIndex])
        }
        // 注意：不再更新相框位置，相框保持固定
      })

      // Initialize states
      setIsBeginning(swiper.isBeginning)
      setIsEnd(swiper.isEnd)

      // 只在初始化时设置一次相框位置
      setTimeout(initializeFramePosition, 300) // 延迟确保图片加载完成
    }
  }, [swiperRef.current, list, setActiveTemplate])

  // Default Swiper parameters
  const defaultSwiperProps = useMemo(
    () => ({
      slidesPerView: isPhone() ? 1.32 : 4,
      spaceBetween: 0,
      loop: multiline ? false : true,
      pagination: false,
      navigation: false, // Disable default navigation since we're using custom buttons
      centeredSlides: true,
      modules: [Grid, Navigation],
      className: 'mySwiper',
      initialSlide: 0,
    }),
    [multiline]
  )

  // Merge default props with custom props, with custom props taking precedence
  const mergedSwiperProps = useMemo(
    () => ({
      ...defaultSwiperProps,
      ...swiperProps,
    }),
    [defaultSwiperProps, swiperProps]
  )

  return (
    <div
      className={classNames(
        'w-full relative py-6 top-[50%] -translate-y-[50%]'
      )}
    >
      {/* Custom navigation buttons - only show when there are enough slides */}
      {list.length > (multiline ? 4 : 2) && (
        <>
          <div
            className={classNames({
              'hidden pointer-events-none': isBeginning,
            })}
          >
            <SwiperPrevButton swiperRef={swiperRef} />
          </div>
          <div
            className={classNames({
              'hidden pointer-events-none': isEnd,
            })}
          >
            <SwiperNextButton swiperRef={swiperRef} />
          </div>
        </>
      )}

      <Swiper
        grid={{ rows: multiline ? 2 : 1, fill: 'row' }}
        {...mergedSwiperProps}
        onSwiper={(swiper: any) => {
          swiperRef.current = swiper
        }}
      >
        {list.map((it, i) => (
          <SwiperSlide
            key={i}
            onClick={() => {
              if (swiperRef.current) {
                swiperRef.current[multiline ? 'slideTo' : 'slideToLoop'](i)
              }
            }}
          >
            {({ isActive }) => (
              <MazeSingleTemplate
                activeGender={activeGender}
                key={i}
                item={it as ThemeDetail}
                active={isActive || it?.id === activeTemplate?.id}
                onSelect={() => void 0}
                className={classNames(
                  'transition-transform duration-300 rounded-[3rem] ipad:rounded-[2rem] ',
                  {
                    'active scale-[1] opacity-100': isActive,
                    'scale-[0.9] opacity-80': !isActive,
                  }
                )}
              />
            )}
          </SwiperSlide>
        ))}
      </Swiper>

      {/* 固定位置的相框覆盖层 - 初始化后位置保持不变 */}
      {activeSlideRect && listKey === 'detail-image-list' && (
        <div
          className="fixed pointer-events-none rounded-3xl object-contain z-50 maze-slide-active-shadow"
          style={{
            left: `${activeSlideRect.left * (screenOrientation.isLandScape ? 0.68 : 1)}px`,
            top: `calc(1.5rem + 16px)`,
            width: `${activeSlideRect.width * (screenOrientation.isLandScape ? 1.25 : 1)}px`,
            height: `${activeSlideRect.height * (screenOrientation.isLandScape ? 1.25 : 1)}px`,
          }}
        >
          <img
            className="w-full h-full ipad:rounded-[2rem]"
            src={publicPreLoadSourceObj.frame}
            alt=""
          />
        </div>
      )}
    </div>
  )
}
export default MazeSingleTemplateList
